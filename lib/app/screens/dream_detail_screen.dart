import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../services/dream_processing_service.dart';
import '../../services/dream_matching_service.dart';
import '../models/dream_match.dart';

class DreamDetailScreen extends StatefulWidget {
  final String dreamId;
  final Map<String, dynamic> dreamData;

  const DreamDetailScreen({
    super.key,
    required this.dreamId,
    required this.dreamData,
  });

  @override
  State<DreamDetailScreen> createState() => _DreamDetailScreenState();
}

class _DreamDetailScreenState extends State<DreamDetailScreen> {
  int _selectedIndex = 1; // Feather icon is selected

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/bg.jpg'),
          fit: BoxFit.cover,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withValues(alpha: 0.4),
                Colors.black.withValues(alpha: 0.6),
              ],
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Row(
                    children: [
                      // Back button
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.chevron_left,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      // Title
                      Expanded(
                        child: Text(
                          widget.dreamData['title'] ?? 'Dream Detail',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Dream content with real-time interpretation updates
                Expanded(
                  child: StreamBuilder<QuerySnapshot>(
                    stream: DreamProcessingService.getInterpretationStream(widget.dreamId),
                    builder: (context, interpretationSnapshot) {
                      final interpretationDoc = interpretationSnapshot.hasData &&
                          interpretationSnapshot.data!.docs.isNotEmpty
                        ? interpretationSnapshot.data!.docs.first
                        : null;

                      return SingleChildScrollView(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: _buildDreamContent(widget.dreamData, interpretationDoc),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: Container(
          height: 90,
          decoration: const BoxDecoration(
            color: Colors.black,
            border: Border(
              top: BorderSide(
                color: Colors.white12,
                width: 0.5,
              ),
            ),
          ),
          child: SafeArea(
            top: false,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildBottomNavItem(0, 'assets/images/logo.png'),
                _buildBottomNavItem(1, 'assets/images/feather.png'),
                _buildBottomNavItem(2, 'assets/images/rainbow.png'),
                _buildBottomNavItem(3, 'assets/images/triangle.png'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDreamContent(Map<String, dynamic> data, DocumentSnapshot? interpretationDoc) {
    final title = data['title'] ?? 'Untitled Dream';
    final description = data['description'] ?? '';
    final dreamType = data['dreamType'] ?? 'Dream';
    final date = (data['date'] as Timestamp?)?.toDate() ?? DateTime.now();
    final tags = List<String>.from(data['tags'] ?? []);
    final sleepQuality = data['sleepQuality']?.toDouble() ?? 50.0;

    // Get dream type icon
    String iconPath = 'assets/images/pyramid.png';
    switch (dreamType) {
      case 'Nightmare':
        iconPath = 'assets/images/triangle(1).png';
        break;
      case 'Lucid':
        iconPath = 'assets/images/triangle(2).png';
        break;
      case 'Prophetic':
        iconPath = 'assets/images/magic-ball.png';
        break;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Dream type and date section
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Dream type icon
              Container(
                width: 60,
                height: 60,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Image.asset(
                  iconPath,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 20),
              // Dream info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      dreamType,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      _formatDate(date),
                      style: const TextStyle(
                        color: Colors.white60,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      'Sleep Quality: ${sleepQuality.round()}%',
                      style: const TextStyle(
                        color: Color(0xFF6C5CE7),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 20),
        
        // Dream description
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Dream Description',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 15),
              Text(
                description,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 20),
        
        // Tags section
        if (tags.isNotEmpty) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Dream Symbols',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 15),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: tags.map((tag) => Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF6C5CE7).withValues(alpha: 0.4),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      tag,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )).toList(),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
        ],
        
        // Dream interpretation section
        _buildInterpretationSection(interpretationDoc),

        const SizedBox(height: 30),

        // Dream matches section - always visible
        _buildDreamMatchesSection(),

        const SizedBox(height: 100), // Extra space for bottom navigation
      ],
    );
  }

  Widget _buildInterpretationSection(DocumentSnapshot? interpretationDoc) {
    if (DreamProcessingService.isInterpretationReady(interpretationDoc)) {
      // Show interpretation
      final interpretation = DreamProcessingService.getInterpretationText(interpretationDoc)!;
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        margin: const EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6C5CE7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Dream Deus AI Interpretation',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              interpretation,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ],
        ),
      );
    } else if (DreamProcessingService.isInterpretationProcessing(interpretationDoc)) {
      // Show processing state
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        margin: const EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
            ),
            const SizedBox(height: 15),
            const Text(
              'AI Dream Interpretation in Progress',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'Our advanced Dream Deus AI is analyzing your dream patterns and symbols. This usually takes 1-3 minutes.',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 14,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    } else if (DreamProcessingService.isInterpretationFailed(interpretationDoc)) {
      // Show error state
      final error = DreamProcessingService.getInterpretationError(interpretationDoc);
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        margin: const EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.red.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 32,
            ),
            const SizedBox(height: 15),
            const Text(
              'Interpretation Failed',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              error ?? 'An error occurred while processing your dream interpretation.',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 14,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 15),
            ElevatedButton(
              onPressed: () {
                // Retry interpretation
                DreamProcessingService.processDreamInterpretation(widget.dreamId);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6C5CE7),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Retry Interpretation'),
            ),
          ],
        ),
      );
    } else {
      // Show manual interpretation button (for all dreams without interpretation)
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        margin: const EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.auto_awesome,
                color: Color(0xFF6C5CE7),
                size: 30,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Dream Deus AI Interpretation',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Unlock the mysteries of your dream with our advanced AI system. Get personalized insights using premium Dream Deus analysis.',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 14,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Start interpretation
                  DreamProcessingService.processDreamInterpretation(widget.dreamId);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6C5CE7),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.psychology, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'Get AI Interpretation',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }
  }



  Widget _buildDreamMatchingSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.people_outline,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Similar Dreams',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),

          // Stream builder for real-time matching results
          StreamBuilder<QuerySnapshot>(
            stream: FirebaseFirestore.instance
                .collection('dreams')
                .doc(widget.dreamId)
                .collection('matches')
                .orderBy('similarityScore', descending: true)
                .limit(5)
                .snapshots(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
                  ),
                );
              }

              if (snapshot.hasError) {
                return Text(
                  'Error loading matches: ${snapshot.error}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                );
              }

              if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                return _buildEnhancedNoMatchesWidget();
              }

              final matches = snapshot.data!.docs
                  .map((doc) => DreamMatch.fromDocument(doc))
                  .toList();



              return Column(
                children: matches.map((match) => _buildMatchCard(match)).toList(),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDreamMatchesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF6C5CE7).withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced section header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.people_outline,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Similar Dreams',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Dreams from users with similar experiences',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Stream builder for real-time matching results
          StreamBuilder<QuerySnapshot>(
            stream: FirebaseFirestore.instance
                .collection('dreams')
                .doc(widget.dreamId)
                .collection('matches')
                .orderBy('similarityScore', descending: true)
                .limit(5)
                .snapshots(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Container(
                  padding: const EdgeInsets.all(30),
                  child: const Center(
                    child: Column(
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
                        ),
                        SizedBox(height: 15),
                        Text(
                          'Finding similar dreams...',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              if (snapshot.hasError) {
                return Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 32,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        'Error loading matches: ${snapshot.error}',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                return _buildEnhancedNoMatchesWidget();
              }

              final matches = snapshot.data!.docs
                  .map((doc) => DreamMatch.fromDocument(doc))
                  .toList();

              return Column(
                children: [
                  // Match count indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${matches.length} similar dream${matches.length != 1 ? 's' : ''} found',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(height: 15),
                  // Match cards
                  ...matches.map((match) => Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: _buildEnhancedMatchCard(match),
                  )).toList(),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedNoMatchesWidget() {
    return Container(
      padding: const EdgeInsets.all(25),
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                  const Color(0xFF6C5CE7).withValues(alpha: 0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(40),
              border: Border.all(
                color: const Color(0xFF6C5CE7).withValues(alpha: 0.4),
                width: 2,
              ),
            ),
            child: const Icon(
              Icons.auto_awesome,
              color: Color(0xFF6C5CE7),
              size: 30,
            ),
          ),
          const SizedBox(height: 15),
          const Text(
            '🌟 Unique Dream Experience!',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            'Congratulations! You\'ve experienced a truly unique dream that stands out from others in our database.',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 14,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'We\'re excited to see what insights your one-of-a-kind dream will reveal through our AI analysis!',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 12,
              height: 1.3,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMatchCard(DreamMatch match) {
    print('DEBUG - Building match card for dreamId: ${match.dreamId}, userId: ${match.userId}');

    return FutureBuilder<Map<String, dynamic>?>(
      future: _getUserProfile(match.userId),
      builder: (context, snapshot) {

        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingMatchCard(match);
        }

        final userProfile = snapshot.data;
        if (userProfile == null) {
          return _buildErrorMatchCard(match);
        }
        return _buildCompleteMatchCard(match, userProfile);
      },
    );
  }

  Widget _buildEnhancedMatchCard(DreamMatch match) {
    return FutureBuilder<Map<String, dynamic>?>(
      future: _getUserProfile(match.userId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildEnhancedLoadingMatchCard(match);
        }

        final userProfile = snapshot.data;
        if (userProfile == null) {
          return _buildEnhancedErrorMatchCard(match);
        }
        return _buildEnhancedCompleteMatchCard(match, userProfile);
      },
    );
  }

  Widget _buildEnhancedCompleteMatchCard(DreamMatch match, Map<String, dynamic> userProfile) {
    return Container(
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.08),
            Colors.white.withValues(alpha: 0.03),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with dream type and similarity
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Image.asset(
                  match.dreamTypeIcon,
                  width: 20,
                  height: 20,
                  color: const Color(0xFF6C5CE7),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  match.dreamType,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF6C5CE7), Color(0xFF8B7ED8)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  match.similarityPercentage,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 14),

          // Dream title
          Text(
            '"${match.title}"',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 15,
              fontWeight: FontWeight.w600,
              height: 1.3,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 12),

          // User demographics
          Row(
            children: [
              Icon(
                Icons.person_outline,
                color: Colors.white.withValues(alpha: 0.6),
                size: 16,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  _buildUserDemographics(userProfile),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedLoadingMatchCard(DreamMatch match) {
    return Container(
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF6C5CE7).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                const Color(0xFF6C5CE7).withValues(alpha: 0.7),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Loading match details...',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.6),
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedErrorMatchCard(DreamMatch match) {
    return Container(
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.withValues(alpha: 0.7),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Error loading match details',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompleteMatchCard(DreamMatch match, Map<String, dynamic> userProfile) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Dream type icon
              Image.asset(
                match.dreamTypeIcon,
                width: 24,
                height: 24,
                color: const Color(0xFF6C5CE7),
              ),
              const SizedBox(width: 10),
              // Similarity percentage
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  match.similarityPercentage,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Spacer(),
              // User location
              Flexible(
                child: Text(
                  _getLocationString(userProfile),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Dream title
          Text(
            '"${match.title}"',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          // Profile description
          Text(
            'This dream was also experienced by:',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '• ${_getProfileDescription(userProfile)}',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 12,
              height: 1.3,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  void _onBottomNavTap(int index) {
    setState(() {
      _selectedIndex = index;
    });

    switch (index) {
      case 0:
        // Home
        Navigator.popUntil(context, (route) => route.isFirst);
        break;
      case 1:
        // Dream Records - go back to records
        Navigator.pop(context);
        break;
      case 2:
        // Rainbow
        break;
      case 3:
        // Triangle
        break;
    }
  }

  Widget _buildBottomNavItem(int index, String iconPath) {
    final isSelected = _selectedIndex == index;
    return GestureDetector(
      onTap: () => _onBottomNavTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        child: Image.asset(
          iconPath,
          height: 32,
          width: 32,
          color: isSelected ? const Color(0xFF6C5CE7) : Colors.white54,
        ),
      ),
    );
  }

  // Get user profile from Firestore
  Future<Map<String, dynamic>?> _getUserProfile(String userId) async {
    try {
      if (userId.isEmpty) {
        print('ERROR - Empty userId provided to _getUserProfile');
        return null;
      }

      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      if (userDoc.exists) {
        return userDoc.data();
      }

      return null;
    } catch (e) {
      print('ERROR - Getting user profile: $e');
      return null;
    }
  }

  // Generate location string from profile
  String _getLocationString(Map<String, dynamic> userProfile) {
    final parts = <String>[];
    final city = userProfile['city'] ?? '';
    final state = userProfile['state'] ?? '';
    final country = userProfile['country'] ?? '';

    if (city.isNotEmpty) parts.add(city);
    if (state.isNotEmpty) parts.add(state);
    if (country.isNotEmpty) parts.add(country);

    return parts.join(', ');
  }

  // Generate profile description from profile data
  String _getProfileDescription(Map<String, dynamic> userProfile) {
    final parts = <String>[];
    final age = userProfile['age'] ?? '';
    final gender = userProfile['gender'] ?? '';
    final country = userProfile['country'] ?? '';
    final educationLevel = userProfile['educationLevel'] ?? '';

    // Age range
    if (age.isNotEmpty) {
      parts.add('a $age year old');
    }

    // Gender
    if (gender.isNotEmpty) {
      parts.add(gender.toLowerCase());
    }

    // Location
    if (country.isNotEmpty) {
      parts.add('from $country');
    }

    // Education
    if (educationLevel.isNotEmpty) {
      String education = educationLevel.toLowerCase();
      if (education.contains('university') || education.contains('bachelor')) {
        education = 'university education';
      } else if (education.contains('master')) {
        education = 'master\'s degree';
      } else if (education.contains('phd') || education.contains('doctorate')) {
        education = 'doctorate degree';
      } else if (education.contains('high school')) {
        education = 'high school education';
      }
      parts.add('with $education');
    }

    if (parts.isEmpty) {
      return 'Someone with similar experiences';
    }

    return parts.join(' ');
  }

  // Loading state for match card
  Widget _buildLoadingMatchCard(DreamMatch match) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image.asset(
                match.dreamTypeIcon,
                width: 24,
                height: 24,
                color: const Color(0xFF6C5CE7),
              ),
              const SizedBox(width: 10),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  match.similarityPercentage,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Spacer(),
              const SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '"${match.title}"',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Text(
            'This dream was also experienced by:',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '• Loading profile...',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 12,
              height: 1.3,
            ),
          ),
        ],
      ),
    );
  }

  // Error state for match card
  Widget _buildErrorMatchCard(DreamMatch match) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image.asset(
                match.dreamTypeIcon,
                width: 24,
                height: 24,
                color: const Color(0xFF6C5CE7),
              ),
              const SizedBox(width: 10),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF6C5CE7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  match.similarityPercentage,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '"${match.title}"',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Text(
            'This dream was also experienced by:',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '• Someone with similar experiences',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 12,
              height: 1.3,
            ),
          ),
        ],
      ),
    );
  }

  String _buildUserDemographics(Map<String, dynamic> userProfile) {
    final demographics = <String>[];

    // Age range - handle both integer and string values
    final age = userProfile['age'];
    if (age != null) {
      String ageRange;

      // If age is already a string (like "46-55"), use it directly
      if (age is String) {
        ageRange = age;
      }
      // If age is a number, convert to range
      else if (age is num) {
        final ageInt = age.toInt();
        if (ageInt < 25) {
          ageRange = '18-24';
        } else if (ageInt < 35) {
          ageRange = '25-34';
        } else if (ageInt < 45) {
          ageRange = '35-44';
        } else if (ageInt < 55) {
          ageRange = '45-54';
        } else {
          ageRange = '55+';
        }
      } else {
        ageRange = age.toString();
      }

      demographics.add(ageRange);
    }

    // Gender
    final gender = userProfile['gender'];
    if (gender != null && gender.toString().isNotEmpty) {
      demographics.add(gender.toString());
    }

    // Country
    final country = userProfile['country'];
    if (country != null && country.toString().isNotEmpty) {
      demographics.add(country.toString());
    }

    // Education level
    final education = userProfile['educationLevel'];
    if (education != null && education.toString().isNotEmpty) {
      demographics.add(education.toString());
    }

    return demographics.isNotEmpty
        ? demographics.join(' • ')
        : 'Anonymous user';
  }
}
