import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;
import '../../models/dream_match.dart';

class MapLocation {
  final DreamMatch match;
  final String country;
  final Map<String, dynamic> userProfile;

  MapLocation({
    required this.match,
    required this.country,
    required this.userProfile,
  });
}



class InteractiveWorldMapWidget extends StatefulWidget {
  final List<DreamMatch> matches;

  const InteractiveWorldMapWidget({
    super.key,
    required this.matches,
  });

  @override
  State<InteractiveWorldMapWidget> createState() => _InteractiveWorldMapWidgetState();
}

class _InteractiveWorldMapWidgetState extends State<InteractiveWorldMapWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;
  late MapController _mapController;

  final List<MapLocation> _locations = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _mapController = MapController();
    _loadUserLocations();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeIn),
    );

    _fadeController.forward();
  }

  Future<void> _loadUserLocations() async {
    final locations = <MapLocation>[];
    final processedUsers = <String>{};
    
    for (final match in widget.matches) {
      // Skip if we already processed this user
      if (processedUsers.contains(match.userId)) continue;
      
      try {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(match.userId)
            .get();
        
        if (userDoc.exists) {
          final userData = userDoc.data()!;
          final country = userData['country'] ?? 'Unknown';
          
          final location = MapLocation(
            match: match,
            country: country,
            userProfile: userData,
          );
          
          locations.add(location);
          processedUsers.add(match.userId);
        }
      } catch (e) {
        print('Error loading user location for ${match.userId}: $e');
      }
    }
    
    if (mounted) {
      setState(() {
        _locations.clear();
        _locations.addAll(locations);
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    _mapController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF6C5CE7).withValues(alpha: 0.8),
            const Color(0xFF2D3436).withValues(alpha: 0.9),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.public,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Global Dream Connections',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${_getUniqueCountries()} countries • ${widget.matches.length} dreamers',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Map
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFF1a1a2e).withValues(alpha: 0.9),
                      const Color(0xFF16213e).withValues(alpha: 0.95),
                    ],
                  ),
                ),
                child: _isLoading 
                    ? _buildLoadingOverlay()
                    : _buildMap(),
              ),
            ),
            // Footer
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.pan_tool,
                    color: Colors.white.withValues(alpha: 0.6),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Pinch to zoom • Tap dots for details',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.6),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMap() {
    return FlutterMap(
      mapController: _mapController,
      options: MapOptions(
        initialCenter: const LatLng(20, 0), // Center of world
        initialZoom: 1.5,
        minZoom: 1.0,
        maxZoom: 6.0,
      ),
      children: [
        // Dark custom tile layer
        TileLayer(
          urlTemplate: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}.png',
          subdomains: const ['a', 'b', 'c', 'd'],
          userAgentPackageName: 'com.example.thedreamdeus',
        ),
        // Dream location markers
        MarkerLayer(
          markers: _buildMarkers(),
        ),
      ],
    );
  }

  List<Marker> _buildMarkers() {
    return _locations.map((location) {
      final coords = _getCountryCoordinates(location.country);
      
      return Marker(
        point: LatLng(coords['lat']!, coords['lon']!),
        width: 40,
        height: 40,
        child: GestureDetector(
          onTap: () => _showLocationDetails([location]),
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: _getColorForSimilarity(location.match.similarityScore),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: _getColorForSimilarity(location.match.similarityScore).withValues(alpha: 0.6),
                        blurRadius: 12,
                        spreadRadius: 4,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );
    }).toList();
  }

  Map<String, double> _getCountryCoordinates(String country) {
    // Country coordinates mapping
    final countryCoords = {
      'Turkey': {'lat': 39.0, 'lon': 35.0},
      'United States': {'lat': 39.8, 'lon': -98.5},
      'Germany': {'lat': 51.2, 'lon': 10.4},
      'France': {'lat': 46.6, 'lon': 2.2},
      'United Kingdom': {'lat': 55.4, 'lon': -3.4},
      'Spain': {'lat': 40.5, 'lon': -3.7},
      'Italy': {'lat': 41.9, 'lon': 12.6},
      'Russia': {'lat': 61.5, 'lon': 105.3},
      'China': {'lat': 35.9, 'lon': 104.2},
      'Japan': {'lat': 36.2, 'lon': 138.3},
      'Australia': {'lat': -25.3, 'lon': 133.8},
      'Brazil': {'lat': -14.2, 'lon': -51.9},
      'Canada': {'lat': 56.1, 'lon': -106.3},
      'India': {'lat': 20.6, 'lon': 78.9},
      'Mexico': {'lat': 23.6, 'lon': -102.6},
      'Argentina': {'lat': -38.4, 'lon': -63.6},
      'South Africa': {'lat': -30.6, 'lon': 22.9},
      'Egypt': {'lat': 26.8, 'lon': 30.8},
      'Nigeria': {'lat': 9.1, 'lon': 8.7},
      'Kenya': {'lat': -0.0, 'lon': 37.9},
      'Thailand': {'lat': 15.9, 'lon': 100.9},
      'Indonesia': {'lat': -0.8, 'lon': 113.9},
      'South Korea': {'lat': 35.9, 'lon': 127.8},
      'Netherlands': {'lat': 52.1, 'lon': 5.3},
      'Sweden': {'lat': 60.1, 'lon': 18.6},
      'Norway': {'lat': 60.5, 'lon': 8.5},
      'Poland': {'lat': 51.9, 'lon': 19.1},
      'Ukraine': {'lat': 48.4, 'lon': 31.2},
      'Iran': {'lat': 32.4, 'lon': 53.7},
      'Saudi Arabia': {'lat': 23.9, 'lon': 45.1},
    };

    return countryCoords[country] ?? {'lat': 0.0, 'lon': 0.0}; // Default center
  }

  Color _getColorForSimilarity(double similarity) {
    final percentage = (similarity * 100).round();
    if (percentage >= 90) return const Color(0xFF00FF88); // Bright green
    if (percentage >= 80) return const Color(0xFF00FFFF); // Cyan
    if (percentage >= 70) return const Color(0xFF0080FF); // Blue
    return const Color(0xFFFF6B6B); // Red
  }

  int _getUniqueCountries() {
    return _locations.map((l) => l.country).toSet().length;
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.3),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
            ),
            SizedBox(height: 16),
            Text(
              'Loading dream connections...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLocationDetails(List<MapLocation> locations) {
    final location = locations.first;
    final profile = location.userProfile;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2D3436),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getColorForSimilarity(location.match.similarityScore).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.location_on,
                color: _getColorForSimilarity(location.match.similarityScore),
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'Dream Match',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Location', location.country),
            _buildDetailRow('Similarity', '${(location.match.similarityScore * 100).round()}%'),
            _buildDetailRow('Age Range', _getAgeRange(profile['age'])),
            _buildDetailRow('Gender', profile['gender'] ?? 'Not specified'),
            _buildDetailRow('Education', profile['educationLevel'] ?? 'Not specified'),
            const SizedBox(height: 16),
            Text(
              'Dream: ${location.match.title}',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Close',
              style: TextStyle(color: Color(0xFF6C5CE7)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getAgeRange(dynamic age) {
    if (age == null) return 'Not specified';
    final ageInt = age is int ? age : int.tryParse(age.toString()) ?? 0;
    if (ageInt < 18) return 'Under 18';
    if (ageInt < 25) return '18-24';
    if (ageInt < 35) return '25-34';
    if (ageInt < 45) return '35-44';
    if (ageInt < 55) return '45-54';
    return '55+';
  }
}
