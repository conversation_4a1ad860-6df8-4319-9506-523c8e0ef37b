import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:math' as math;
import '../../models/dream_match.dart';

class InteractiveWorldMapWidget extends StatefulWidget {
  final List<DreamMatch> matches;

  const InteractiveWorldMapWidget({
    super.key,
    required this.matches,
  });

  @override
  State<InteractiveWorldMapWidget> createState() => _InteractiveWorldMapWidgetState();
}

class _InteractiveWorldMapWidgetState extends State<InteractiveWorldMapWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _fadeAnimation;

  final TransformationController _transformationController = TransformationController();
  final List<MapLocation> _locations = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadUserLocations();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    ); // Remove auto-repeat for manual control

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
  }

  Future<void> _loadUserLocations() async {
    final locations = <MapLocation>[];
    final processedUsers = <String>{};

    for (final match in widget.matches) {
      // Skip if we already processed this user
      if (processedUsers.contains(match.userId)) continue;

      try {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(match.userId)
            .get();

        if (userDoc.exists) {
          final userData = userDoc.data()!;
          final country = userData['country'] ?? 'Unknown';

          final location = MapLocation(
            match: match,
            country: country,
            userProfile: userData,
          );

          locations.add(location);
          processedUsers.add(match.userId);
        }
      } catch (e) {
        print('Error loading user location for ${match.userId}: $e');
      }
    }

    if (mounted) {
      setState(() {
        _locations.clear();
        _locations.addAll(locations);
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _fadeController.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.matches.isEmpty) {
      return const SizedBox.shrink();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        height: 300,
        margin: const EdgeInsets.only(top: 15),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: Stack(
            children: [
              // Interactive map
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFF1a1a2e).withValues(alpha: 0.9),
                      const Color(0xFF16213e).withValues(alpha: 0.95),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background globe with pan/zoom
                    InteractiveViewer(
                      transformationController: _transformationController,
                      minScale: 0.8,
                      maxScale: 3.0,
                      panEnabled: true,
                      scaleEnabled: true,
                      onInteractionUpdate: (details) {
                        // Update rotation based on pan
                        final delta = details.focalPointDelta;
                        if (delta.dx.abs() > 1) {
                          final rotationDelta = delta.dx / 200; // Sensitivity
                          _rotationController.value = (_rotationController.value + rotationDelta) % 1.0;
                        }
                      },
                      child: AnimatedBuilder(
                        animation: Listenable.merge([_pulseAnimation, _rotationAnimation]),
                        builder: (context, child) {
                          return CustomPaint(
                            painter: WorldMapPainter(
                              locations: _locations,
                              pulseAnimation: _pulseAnimation,
                              rotationAnimation: _rotationAnimation,
                            ),
                            size: Size.infinite,
                          );
                        },
                      ),
                    ),
                    // Interactive layer on top (outside InteractiveViewer)
                    if (!_isLoading)
                      AnimatedBuilder(
                        animation: _rotationAnimation,
                        builder: (context, child) => _buildInteractionLayer(),
                      ),
                    if (_isLoading) _buildLoadingOverlay(),
                  ],
                ),
              ),

              // Header overlay
              _buildHeader(),

              // Instructions overlay
              _buildInstructions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Positioned(
      top: 15,
      left: 15,
      right: 15,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF6C5CE7).withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.public,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Global Dream Connections',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${_getUniqueCountries()} countries • ${widget.matches.length} dreamers',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 15,
      left: 15,
      right: 15,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.touch_app,
              color: Colors.white.withValues(alpha: 0.7),
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Pinch to zoom • Tap dots for details',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
          ),
          SizedBox(height: 15),
          Text(
            'Loading dream locations...',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionLayer() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final center = Offset(constraints.maxWidth / 2, constraints.maxHeight / 2);
        final radius = math.min(constraints.maxWidth, constraints.maxHeight) * 0.35;

        return Stack(
          children: _locations.map((location) {
            return _buildLocationButton(location, center, radius);
          }).toList(),
        );
      },
    );
  }

  Widget _buildLocationButton(MapLocation location, Offset center, double radius) {
    final pos = _getLocationPosition(location, center, radius);

    if (pos == null) return const SizedBox.shrink();

    return Positioned(
      left: pos.dx - 20, // Button size / 2
      top: pos.dy - 20,
      child: GestureDetector(
        onTap: () => _showLocationDetails([location]),
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.transparent,
          ),
          child: Center(
            child: Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: _getColorForSimilarity(location.match.similarityScore),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: _getColorForSimilarity(location.match.similarityScore).withValues(alpha: 0.6),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }



  Offset? _getLocationPosition(MapLocation location, Offset center, double radius) {
    final coords = _getCountryLatLon(location.country);
    return _latLonToPosition(coords['lat']!, coords['lon']!, center, radius);
  }

  Offset? _latLonToPosition(double lat, double lon, Offset center, double radius) {
    final latRad = lat * math.pi / 180;
    final lonRad = (lon + _rotationAnimation.value * 180) * math.pi / 180;

    final x = radius * math.cos(latRad) * math.cos(lonRad);
    final y = -radius * math.sin(latRad);
    final z = radius * math.cos(latRad) * math.sin(lonRad);

    if (z < 0) return null;

    return Offset(center.dx + x, center.dy + y);
  }

  Map<String, double> _getCountryLatLon(String country) {
    final countryCoords = {
      'Turkey': {'lat': 39.0, 'lon': 35.0},
      'United States': {'lat': 40.0, 'lon': -95.0},
      'Germany': {'lat': 51.0, 'lon': 9.0},
      'France': {'lat': 46.0, 'lon': 2.0},
      'United Kingdom': {'lat': 54.0, 'lon': -2.0},
      'Spain': {'lat': 40.0, 'lon': -4.0},
      'Italy': {'lat': 42.0, 'lon': 12.0},
      'Russia': {'lat': 60.0, 'lon': 100.0},
      'China': {'lat': 35.0, 'lon': 105.0},
      'Japan': {'lat': 36.0, 'lon': 138.0},
      'Australia': {'lat': -25.0, 'lon': 135.0},
      'Brazil': {'lat': -10.0, 'lon': -55.0},
      'Canada': {'lat': 60.0, 'lon': -95.0},
      'India': {'lat': 20.0, 'lon': 77.0},
    };

    return countryCoords[country] ?? {'lat': 0.0, 'lon': 0.0};
  }

  int _getUniqueCountries() {
    return _locations.map((l) => l.country).toSet().length;
  }

  Color _getColorForSimilarity(double similarity) {
    final percentage = (similarity * 100).round();
    if (percentage >= 90) return const Color(0xFF00FF88); // Bright green
    if (percentage >= 80) return const Color(0xFF00FFFF); // Cyan
    if (percentage >= 70) return const Color(0xFF0080FF); // Blue
    return const Color(0xFFFF6B6B); // Red
  }



  void _showLocationDetails(List<MapLocation> locations) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildLocationModal(locations),
    );
  }
  Widget _buildLocationModal(List<MapLocation> locations) {
    final country = locations.first.country;

    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Color(0xFF1a1a2e),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6C5CE7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.location_on,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        country,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${locations.length} dreamer${locations.length != 1 ? 's' : ''} with similar experiences',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white60,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: locations.length,
              itemBuilder: (context, index) {
                final location = locations[index];
                return _buildLocationCard(location);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationCard(MapLocation location) {
    final match = location.match;
    final profile = location.userProfile;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getColorForSimilarity(match.similarityScore).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Image.asset(
                match.dreamTypeIcon,
                width: 24,
                height: 24,
                color: _getColorForSimilarity(match.similarityScore),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  match.dreamType,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getColorForSimilarity(match.similarityScore),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  match.similarityPercentage,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Dream title
          Text(
            '"${match.title}"',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 15,
              fontWeight: FontWeight.w500,
              height: 1.3,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 12),

          // Demographics
          Row(
            children: [
              Icon(
                Icons.person_outline,
                color: Colors.white.withValues(alpha: 0.6),
                size: 16,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  _buildDemographics(profile),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _buildDemographics(Map<String, dynamic> profile) {
    final demographics = <String>[];

    // Age
    final age = profile['age'];
    if (age != null) {
      if (age is String) {
        demographics.add(age);
      } else if (age is num) {
        final ageInt = age.toInt();
        if (ageInt < 25) {
          demographics.add('18-24');
        } else if (ageInt < 35) {
          demographics.add('25-34');
        } else if (ageInt < 45) {
          demographics.add('35-44');
        } else if (ageInt < 55) {
          demographics.add('45-54');
        } else {
          demographics.add('55+');
        }
      }
    }

    // Gender
    if (profile['gender'] != null) {
      demographics.add(profile['gender']);
    }

    // Education
    if (profile['educationLevel'] != null) {
      demographics.add(profile['educationLevel']);
    }

    return demographics.isNotEmpty
        ? demographics.join(' • ')
        : 'Anonymous dreamer';
  }


}

// Helper class for location data
class MapLocation {
  final DreamMatch match;
  final String country;
  final Map<String, dynamic> userProfile;

  MapLocation({
    required this.match,
    required this.country,
    required this.userProfile,
  });
}

// 3D Globe painter with network effect
class WorldMapPainter extends CustomPainter {
  final List<MapLocation> locations;
  final Animation<double> pulseAnimation;
  final Animation<double> rotationAnimation;

  WorldMapPainter({
    required this.locations,
    required this.pulseAnimation,
    required this.rotationAnimation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) * 0.35;

    // 1. Draw outer glow
    _drawOuterGlow(canvas, center, radius);

    // 2. Draw 3D sphere
    _draw3DSphere(canvas, center, radius);

    // 3. Draw network grid
    _drawNetworkGrid(canvas, center, radius);

    // 4. Draw continents with dots
    _drawContinentsWithDots(canvas, center, radius);

    // 5. Draw connection lines
    _drawConnectionLines(canvas, center, radius);

    // 6. Draw location points
    _drawLocationPoints(canvas, center, radius);
  }

  void _drawOuterGlow(Canvas canvas, Offset center, double radius) {
    // Multiple glow layers for depth
    for (int i = 3; i >= 1; i--) {
      final glowRadius = radius * (1.0 + i * 0.15);
      final glowPaint = Paint()
        ..shader = RadialGradient(
          colors: [
            const Color(0xFF00FFFF).withValues(alpha: 0.1 / i),
            const Color(0xFF0080FF).withValues(alpha: 0.05 / i),
            Colors.transparent,
          ],
          stops: [0.0, 0.6, 1.0],
        ).createShader(Rect.fromCircle(center: center, radius: glowRadius));

      canvas.drawCircle(center, glowRadius, glowPaint);
    }
  }

  void _draw3DSphere(Canvas canvas, Offset center, double radius) {
    // Dark sphere background with gradient
    final spherePaint = Paint()
      ..shader = RadialGradient(
        center: const Alignment(-0.3, -0.3), // Light source position
        colors: [
          const Color(0xFF001a33), // Lighter blue
          const Color(0xFF000d1a), // Darker blue
          const Color(0xFF000408), // Almost black
        ],
        stops: [0.0, 0.7, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: radius));

    canvas.drawCircle(center, radius, spherePaint);

    // Rim light effect
    final rimPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..shader = SweepGradient(
        colors: [
          Colors.transparent,
          const Color(0xFF00FFFF).withValues(alpha: 0.8),
          const Color(0xFF0080FF).withValues(alpha: 0.6),
          Colors.transparent,
          Colors.transparent,
        ],
        stops: [0.0, 0.3, 0.5, 0.7, 1.0],
        transform: GradientRotation(rotationAnimation.value * 2 * math.pi),
      ).createShader(Rect.fromCircle(center: center, radius: radius));

    canvas.drawCircle(center, radius, rimPaint);
  }

  void _drawNetworkGrid(Canvas canvas, Offset center, double radius) {
    final gridPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.15)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    // Latitude lines
    for (int i = 1; i < 6; i++) {
      final y = center.dy - radius + (radius * 2 * i / 6);
      final lineRadius = math.sqrt(radius * radius - math.pow(y - center.dy, 2));

      if (lineRadius > 0) {
        canvas.drawArc(
          Rect.fromCircle(center: Offset(center.dx, y), radius: lineRadius),
          0,
          math.pi * 2,
          false,
          gridPaint,
        );
      }
    }

    // Longitude lines
    for (int i = 0; i < 8; i++) {
      final angle = (i * math.pi * 2 / 8) + rotationAnimation.value * 0.5;
      final path = Path();

      for (double t = 0; t <= 1; t += 0.01) {
        final phi = t * math.pi; // 0 to π
        final x = center.dx + radius * math.sin(phi) * math.cos(angle);
        final y = center.dy - radius * math.cos(phi);

        if (t == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }

      canvas.drawPath(path, gridPaint);
    }
  }

  void _drawContinentsWithDots(Canvas canvas, Offset center, double radius) {
    final dotPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.15)
      ..style = PaintingStyle.fill;

    // Draw continent outlines as simple shapes (always visible)
    _drawContinentShapes(canvas, center, radius);
  }

  void _drawContinentShapes(Canvas canvas, Offset center, double radius) {
    final continentPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.15)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.3)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    // Larger, more visible continent shapes
    final continentPaths = [
      // North America
      _createContinentPath(center, radius, [
        [-0.8, -0.4], [-0.4, -0.5], [-0.3, -0.3], [-0.4, 0.2], [-0.8, 0.1]
      ]),
      // South America
      _createContinentPath(center, radius, [
        [-0.5, 0.2], [-0.4, 0.1], [-0.3, 0.6], [-0.5, 0.7], [-0.6, 0.4]
      ]),
      // Europe
      _createContinentPath(center, radius, [
        [-0.1, -0.5], [0.3, -0.6], [0.4, -0.4], [0.2, -0.3], [0.0, -0.3]
      ]),
      // Africa
      _createContinentPath(center, radius, [
        [0.1, -0.3], [0.4, -0.4], [0.5, 0.3], [0.3, 0.6], [0.0, 0.2]
      ]),
      // Asia
      _createContinentPath(center, radius, [
        [0.4, -0.6], [0.9, -0.5], [1.0, 0.1], [0.6, 0.2], [0.4, -0.3]
      ]),
      // Australia
      _createContinentPath(center, radius, [
        [0.7, 0.4], [1.0, 0.3], [1.1, 0.6], [0.8, 0.7]
      ]),
    ];

    for (final path in continentPaths) {
      canvas.drawPath(path, continentPaint);
      canvas.drawPath(path, borderPaint);
    }
  }

  Path _createContinentPath(Offset center, double radius, List<List<double>> points) {
    final path = Path();
    for (int i = 0; i < points.length; i++) {
      final x = center.dx + points[i][0] * radius;
      final y = center.dy + points[i][1] * radius;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    return path;
  }
  void _drawConnectionLines(Canvas canvas, Offset center, double radius) {
    if (locations.length < 2) return;

    final connectionPaint = Paint()
      ..color = const Color(0xFF00FFFF).withValues(alpha: 0.4)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw connections between nearby locations
    for (int i = 0; i < locations.length; i++) {
      for (int j = i + 1; j < locations.length; j++) {
        final pos1 = _getLocationPosition(locations[i], center, radius);
        final pos2 = _getLocationPosition(locations[j], center, radius);

        if (pos1 != null && pos2 != null) {
          // Draw curved connection line
          final path = Path();
          path.moveTo(pos1.dx, pos1.dy);

          // Create arc for connection
          final midPoint = Offset(
            (pos1.dx + pos2.dx) / 2,
            (pos1.dy + pos2.dy) / 2 - 20, // Curve upward
          );

          path.quadraticBezierTo(midPoint.dx, midPoint.dy, pos2.dx, pos2.dy);
          canvas.drawPath(path, connectionPaint);
        }
      }
    }
  }

  void _drawLocationPoints(Canvas canvas, Offset center, double radius) {
    for (final location in locations) {
      final pos = _getLocationPosition(location, center, radius);
      if (pos != null) {
        final similarity = location.match.similarityScore;
        final color = _getColorForSimilarity(similarity);

        // Pulsing effect
        final pulseSize = 6 + (pulseAnimation.value * 4);

        // Outer glow
        final glowPaint = Paint()
          ..color = color.withValues(alpha: 0.3)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

        canvas.drawCircle(pos, pulseSize * 1.5, glowPaint);

        // Main dot
        final dotPaint = Paint()
          ..color = color
          ..style = PaintingStyle.fill;

        canvas.drawCircle(pos, pulseSize, dotPaint);

        // Inner highlight
        final highlightPaint = Paint()
          ..color = Colors.white.withValues(alpha: 0.8)
          ..style = PaintingStyle.fill;

        canvas.drawCircle(pos, pulseSize * 0.3, highlightPaint);
      }
    }
  }

  Offset? _getLocationPosition(MapLocation location, Offset center, double radius) {
    // Get approximate coordinates for country
    final coords = _getCountryLatLon(location.country);
    return _latLonToPosition(coords['lat']!, coords['lon']!, center, radius);
  }

  Offset? _latLonToPosition(double lat, double lon, Offset center, double radius) {
    // Convert latitude/longitude to 3D sphere position
    final latRad = lat * math.pi / 180;
    final lonRad = (lon + rotationAnimation.value * 180) * math.pi / 180;

    // 3D to 2D projection
    final x = radius * math.cos(latRad) * math.cos(lonRad);
    final y = -radius * math.sin(latRad);
    final z = radius * math.cos(latRad) * math.sin(lonRad);

    // Only show points on front hemisphere
    if (z < 0) return null;

    return Offset(center.dx + x, center.dy + y);
  }

  Map<String, double> _getCountryLatLon(String country) {
    final countryCoords = {
      'Turkey': {'lat': 39.0, 'lon': 35.0},
      'United States': {'lat': 40.0, 'lon': -95.0},
      'Germany': {'lat': 51.0, 'lon': 9.0},
      'France': {'lat': 46.0, 'lon': 2.0},
      'United Kingdom': {'lat': 54.0, 'lon': -2.0},
      'Spain': {'lat': 40.0, 'lon': -4.0},
      'Italy': {'lat': 42.0, 'lon': 12.0},
      'Russia': {'lat': 60.0, 'lon': 100.0},
      'China': {'lat': 35.0, 'lon': 105.0},
      'Japan': {'lat': 36.0, 'lon': 138.0},
      'Australia': {'lat': -25.0, 'lon': 135.0},
      'Brazil': {'lat': -10.0, 'lon': -55.0},
      'Canada': {'lat': 60.0, 'lon': -95.0},
      'India': {'lat': 20.0, 'lon': 77.0},
      'Mexico': {'lat': 23.0, 'lon': -102.0},
      'Argentina': {'lat': -34.0, 'lon': -64.0},
      'South Africa': {'lat': -29.0, 'lon': 24.0},
      'Egypt': {'lat': 26.0, 'lon': 30.0},
      'Nigeria': {'lat': 10.0, 'lon': 8.0},
      'Kenya': {'lat': 0.0, 'lon': 38.0},
      'Thailand': {'lat': 15.0, 'lon': 100.0},
      'Indonesia': {'lat': -5.0, 'lon': 120.0},
      'South Korea': {'lat': 36.0, 'lon': 128.0},
      'Netherlands': {'lat': 52.0, 'lon': 5.0},
      'Sweden': {'lat': 60.0, 'lon': 18.0},
      'Norway': {'lat': 62.0, 'lon': 10.0},
      'Poland': {'lat': 52.0, 'lon': 20.0},
      'Ukraine': {'lat': 49.0, 'lon': 32.0},
      'Iran': {'lat': 32.0, 'lon': 53.0},
      'Saudi Arabia': {'lat': 24.0, 'lon': 45.0},
    };

    return countryCoords[country] ?? {'lat': 0.0, 'lon': 0.0};
  }

  Color _getColorForSimilarity(double similarity) {
    final percentage = (similarity * 100).round();
    if (percentage >= 90) return const Color(0xFF00FF88); // Bright green
    if (percentage >= 80) return const Color(0xFF00FFFF); // Cyan
    if (percentage >= 70) return const Color(0xFF0080FF); // Blue
    return const Color(0xFFFF6B6B); // Red
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! WorldMapPainter ||
           oldDelegate.pulseAnimation.value != pulseAnimation.value ||
           oldDelegate.rotationAnimation.value != rotationAnimation.value;
  }
}