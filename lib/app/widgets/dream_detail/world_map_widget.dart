import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:math' as math;
import '../../models/dream_match.dart';

class InteractiveWorldMapWidget extends StatefulWidget {
  final List<DreamMatch> matches;

  const InteractiveWorldMapWidget({
    super.key,
    required this.matches,
  });

  @override
  State<InteractiveWorldMapWidget> createState() => _InteractiveWorldMapWidgetState();
}

class _InteractiveWorldMapWidgetState extends State<InteractiveWorldMapWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  final TransformationController _transformationController = TransformationController();
  final Map<String, List<MapLocation>> _locationClusters = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadUserLocations();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
  }

  Future<void> _loadUserLocations() async {
    final clusters = <String, List<MapLocation>>{};

    for (final match in widget.matches) {
      try {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(match.userId)
            .get();

        if (userDoc.exists) {
          final userData = userDoc.data()!;
          final country = userData['country'] ?? 'Unknown';

          final location = MapLocation(
            match: match,
            country: country,
            userProfile: userData,
          );

          if (clusters[country] == null) {
            clusters[country] = [];
          }
          clusters[country]!.add(location);
        }
      } catch (e) {
        print('Error loading user location for ${match.userId}: $e');
      }
    }

    if (mounted) {
      setState(() {
        _locationClusters.clear();
        _locationClusters.addAll(clusters);
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.matches.isEmpty) {
      return const SizedBox.shrink();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        height: 300,
        margin: const EdgeInsets.only(top: 15),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: Stack(
            children: [
              // Interactive map
              InteractiveViewer(
                transformationController: _transformationController,
                minScale: 0.8,
                maxScale: 3.0,
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0xFF1a1a2e).withValues(alpha: 0.9),
                        const Color(0xFF16213e).withValues(alpha: 0.95),
                      ],
                    ),
                  ),
                  child: CustomPaint(
                    painter: WorldMapPainter(),
                    child: _isLoading
                        ? _buildLoadingOverlay()
                        : _buildLocationDots(),
                  ),
                ),
              ),

              // Header overlay
              _buildHeader(),

              // Instructions overlay
              _buildInstructions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Positioned(
      top: 15,
      left: 15,
      right: 15,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF6C5CE7).withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.public,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Global Dream Connections',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${_locationClusters.length} countries • ${widget.matches.length} dreamers',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 15,
      left: 15,
      right: 15,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.touch_app,
              color: Colors.white.withValues(alpha: 0.7),
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Pinch to zoom • Tap dots for details',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
          ),
          SizedBox(height: 15),
          Text(
            'Loading dream locations...',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationDots() {
    return Stack(
      children: _locationClusters.entries.map((entry) {
        final country = entry.key;
        final locations = entry.value;
        final coordinates = _getCountryCoordinates(country);

        return _buildLocationCluster(coordinates, locations);
      }).toList(),
    );
  }

  Widget _buildLocationCluster(Map<String, double> coordinates, List<MapLocation> locations) {
    final clusterSize = locations.length;
    final maxSimilarity = locations.map((l) => l.match.similarityScore).reduce(math.max);

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Positioned(
          left: coordinates['x']! - (clusterSize > 1 ? 12 : 8),
          top: coordinates['y']! - (clusterSize > 1 ? 12 : 8),
          child: GestureDetector(
            onTap: () => _showLocationDetails(locations),
            child: Container(
              width: clusterSize > 1 ? 24 : 16,
              height: clusterSize > 1 ? 24 : 16,
              decoration: BoxDecoration(
                color: _getColorForSimilarity(maxSimilarity),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: _getColorForSimilarity(maxSimilarity).withValues(alpha: 0.6),
                    blurRadius: 12 * _pulseAnimation.value,
                    spreadRadius: 4 * _pulseAnimation.value,
                  ),
                ],
              ),
              child: Center(
                child: clusterSize > 1
                    ? Text(
                        '$clusterSize',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getColorForSimilarity(double similarity) {
    final percentage = (similarity * 100).round();
    if (percentage >= 90) return const Color(0xFF00FF88); // Bright green
    if (percentage >= 80) return const Color(0xFF6C5CE7); // Purple
    if (percentage >= 70) return const Color(0xFF00BFFF); // Blue
    return const Color(0xFFFF6B6B); // Red
  }

  void _showLocationDetails(List<MapLocation> locations) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildLocationModal(locations),
    );
  }
  Widget _buildLocationModal(List<MapLocation> locations) {
    final country = locations.first.country;

    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Color(0xFF1a1a2e),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6C5CE7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.location_on,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        country,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${locations.length} dreamer${locations.length != 1 ? 's' : ''} with similar experiences',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white60,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: locations.length,
              itemBuilder: (context, index) {
                final location = locations[index];
                return _buildLocationCard(location);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationCard(MapLocation location) {
    final match = location.match;
    final profile = location.userProfile;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getColorForSimilarity(match.similarityScore).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Image.asset(
                match.dreamTypeIcon,
                width: 24,
                height: 24,
                color: _getColorForSimilarity(match.similarityScore),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  match.dreamType,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getColorForSimilarity(match.similarityScore),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  match.similarityPercentage,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Dream title
          Text(
            '"${match.title}"',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 15,
              fontWeight: FontWeight.w500,
              height: 1.3,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 12),

          // Demographics
          Row(
            children: [
              Icon(
                Icons.person_outline,
                color: Colors.white.withValues(alpha: 0.6),
                size: 16,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  _buildDemographics(profile),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _buildDemographics(Map<String, dynamic> profile) {
    final demographics = <String>[];

    // Age
    final age = profile['age'];
    if (age != null) {
      if (age is String) {
        demographics.add(age);
      } else if (age is num) {
        final ageInt = age.toInt();
        if (ageInt < 25) {
          demographics.add('18-24');
        } else if (ageInt < 35) {
          demographics.add('25-34');
        } else if (ageInt < 45) {
          demographics.add('35-44');
        } else if (ageInt < 55) {
          demographics.add('45-54');
        } else {
          demographics.add('55+');
        }
      }
    }

    // Gender
    if (profile['gender'] != null) {
      demographics.add(profile['gender']);
    }

    // Education
    if (profile['educationLevel'] != null) {
      demographics.add(profile['educationLevel']);
    }

    return demographics.isNotEmpty
        ? demographics.join(' • ')
        : 'Anonymous dreamer';
  }

  Map<String, double> _getCountryCoordinates(String country) {
    // Comprehensive country coordinates mapping (relative to widget size)
    final countryCoords = {
      'Turkey': {'x': 280.0, 'y': 120.0},
      'United States': {'x': 120.0, 'y': 130.0},
      'Germany': {'x': 260.0, 'y': 100.0},
      'France': {'x': 250.0, 'y': 110.0},
      'United Kingdom': {'x': 240.0, 'y': 95.0},
      'Spain': {'x': 240.0, 'y': 125.0},
      'Italy': {'x': 265.0, 'y': 125.0},
      'Russia': {'x': 320.0, 'y': 80.0},
      'China': {'x': 360.0, 'y': 125.0},
      'Japan': {'x': 400.0, 'y': 130.0},
      'Australia': {'x': 390.0, 'y': 200.0},
      'Brazil': {'x': 160.0, 'y': 180.0},
      'Canada': {'x': 120.0, 'y': 80.0},
      'India': {'x': 330.0, 'y': 150.0},
      'Mexico': {'x': 100.0, 'y': 150.0},
      'Argentina': {'x': 160.0, 'y': 220.0},
      'South Africa': {'x': 280.0, 'y': 200.0},
      'Egypt': {'x': 280.0, 'y': 140.0},
      'Nigeria': {'x': 260.0, 'y': 160.0},
      'Kenya': {'x': 290.0, 'y': 170.0},
      'Thailand': {'x': 350.0, 'y': 160.0},
      'Indonesia': {'x': 370.0, 'y': 170.0},
      'South Korea': {'x': 380.0, 'y': 125.0},
      'Netherlands': {'x': 250.0, 'y': 95.0},
      'Sweden': {'x': 260.0, 'y': 80.0},
      'Norway': {'x': 255.0, 'y': 75.0},
      'Poland': {'x': 270.0, 'y': 95.0},
      'Ukraine': {'x': 290.0, 'y': 100.0},
      'Iran': {'x': 310.0, 'y': 130.0},
      'Saudi Arabia': {'x': 300.0, 'y': 145.0},
    };

    return countryCoords[country] ?? {'x': 250.0, 'y': 130.0}; // Default center
  }
}

// Helper class for location data
class MapLocation {
  final DreamMatch match;
  final String country;
  final Map<String, dynamic> userProfile;

  MapLocation({
    required this.match,
    required this.country,
    required this.userProfile,
  });
}

// World map painter
class WorldMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    _drawContinents(canvas, size);
  }

  void _drawContinents(Canvas canvas, Size size) {
    final continentPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.08)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.15)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // North America
    final northAmerica = Path()
      ..moveTo(size.width * 0.05, size.height * 0.35)
      ..lineTo(size.width * 0.25, size.height * 0.25)
      ..lineTo(size.width * 0.35, size.height * 0.3)
      ..lineTo(size.width * 0.3, size.height * 0.65)
      ..lineTo(size.width * 0.1, size.height * 0.7)
      ..close();
    canvas.drawPath(northAmerica, continentPaint);
    canvas.drawPath(northAmerica, borderPaint);

    // South America
    final southAmerica = Path()
      ..moveTo(size.width * 0.2, size.height * 0.65)
      ..lineTo(size.width * 0.28, size.height * 0.6)
      ..lineTo(size.width * 0.25, size.height * 0.9)
      ..lineTo(size.width * 0.15, size.height * 0.95)
      ..close();
    canvas.drawPath(southAmerica, continentPaint);
    canvas.drawPath(southAmerica, borderPaint);

    // Europe
    final europe = Path()
      ..moveTo(size.width * 0.45, size.height * 0.25)
      ..lineTo(size.width * 0.55, size.height * 0.2)
      ..lineTo(size.width * 0.6, size.height * 0.4)
      ..lineTo(size.width * 0.48, size.height * 0.45)
      ..close();
    canvas.drawPath(europe, continentPaint);
    canvas.drawPath(europe, borderPaint);

    // Africa
    final africa = Path()
      ..moveTo(size.width * 0.48, size.height * 0.45)
      ..lineTo(size.width * 0.58, size.height * 0.4)
      ..lineTo(size.width * 0.6, size.height * 0.8)
      ..lineTo(size.width * 0.5, size.height * 0.85)
      ..close();
    canvas.drawPath(africa, continentPaint);
    canvas.drawPath(africa, borderPaint);

    // Asia
    final asia = Path()
      ..moveTo(size.width * 0.6, size.height * 0.2)
      ..lineTo(size.width * 0.9, size.height * 0.15)
      ..lineTo(size.width * 0.95, size.height * 0.6)
      ..lineTo(size.width * 0.65, size.height * 0.65)
      ..close();
    canvas.drawPath(asia, continentPaint);
    canvas.drawPath(asia, borderPaint);

    // Australia
    final australia = Path()
      ..moveTo(size.width * 0.75, size.height * 0.75)
      ..lineTo(size.width * 0.85, size.height * 0.73)
      ..lineTo(size.width * 0.87, size.height * 0.83)
      ..lineTo(size.width * 0.77, size.height * 0.85)
      ..close();
    canvas.drawPath(australia, continentPaint);
    canvas.drawPath(australia, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}