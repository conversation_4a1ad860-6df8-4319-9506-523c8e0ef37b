import 'package:flutter/material.dart';
import 'dart:math' as math;

class WorldMapWidget extends StatefulWidget {
  final List<Map<String, dynamic>> userLocations;

  const WorldMapWidget({
    super.key,
    required this.userLocations,
  });

  @override
  State<WorldMapWidget> createState() => _WorldMapWidgetState();
}

class _WorldMapWidgetState extends State<WorldMapWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.userLocations.isEmpty) {
      return const SizedBox.shrink();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        height: 200,
        margin: const EdgeInsets.only(top: 15),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: Stack(
            children: [
              // World map background
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFF1a1a2e).withValues(alpha: 0.8),
                      const Color(0xFF16213e).withValues(alpha: 0.9),
                    ],
                  ),
                ),
                child: CustomPaint(
                  painter: WorldMapPainter(),
                  size: Size.infinite,
                ),
              ),
              
              // Location dots
              ...widget.userLocations.asMap().entries.map((entry) {
                final index = entry.key;
                final location = entry.value;
                return _buildLocationDot(location, index);
              }).toList(),
              
              // Header overlay
              Positioned(
                top: 15,
                left: 15,
                right: 15,
                child: Row(
                  children: [
                    const Icon(
                      Icons.public,
                      color: Color(0xFF6C5CE7),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Dream Locations',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6C5CE7).withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${widget.userLocations.length} location${widget.userLocations.length != 1 ? 's' : ''}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLocationDot(Map<String, dynamic> location, int index) {
    // Convert country to approximate coordinates (simplified)
    final coordinates = _getCountryCoordinates(location['country'] ?? '');
    
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Positioned(
          left: coordinates['x']! - 8,
          top: coordinates['y']! - 8,
          child: GestureDetector(
            onTap: () => _showLocationInfo(location),
            child: Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: const Color(0xFF6C5CE7),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF6C5CE7).withValues(alpha: 0.6),
                    blurRadius: 8 * _pulseAnimation.value,
                    spreadRadius: 2 * _pulseAnimation.value,
                  ),
                ],
              ),
              child: Center(
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showLocationInfo(Map<String, dynamic> location) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1a1a2e),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        title: const Text(
          'Dream Location',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Country: ${location['country']}',
              style: const TextStyle(color: Colors.white70),
            ),
            if (location['ageRange'] != null)
              Text(
                'Age: ${location['ageRange']}',
                style: const TextStyle(color: Colors.white70),
              ),
            if (location['gender'] != null)
              Text(
                'Gender: ${location['gender']}',
                style: const TextStyle(color: Colors.white70),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Close',
              style: TextStyle(color: Color(0xFF6C5CE7)),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, double> _getCountryCoordinates(String country) {
    // Simplified country coordinates mapping (relative to widget size)
    // In a real app, you'd use a proper mapping library
    final countryCoords = {
      'Turkey': {'x': 180.0, 'y': 80.0},
      'United States': {'x': 80.0, 'y': 90.0},
      'Germany': {'x': 160.0, 'y': 70.0},
      'France': {'x': 150.0, 'y': 75.0},
      'United Kingdom': {'x': 145.0, 'y': 65.0},
      'Spain': {'x': 145.0, 'y': 85.0},
      'Italy': {'x': 165.0, 'y': 85.0},
      'Russia': {'x': 200.0, 'y': 60.0},
      'China': {'x': 220.0, 'y': 85.0},
      'Japan': {'x': 250.0, 'y': 90.0},
      'Australia': {'x': 240.0, 'y': 140.0},
      'Brazil': {'x': 100.0, 'y': 120.0},
      'Canada': {'x': 80.0, 'y': 60.0},
      'India': {'x': 200.0, 'y': 100.0},
    };

    return countryCoords[country] ?? {'x': 150.0, 'y': 90.0}; // Default center
  }
}

class WorldMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Draw simplified world map outline
    final path = Path();
    
    // Draw continents as simplified shapes
    _drawContinents(canvas, size, paint);
  }

  void _drawContinents(Canvas canvas, Size size, Paint paint) {
    // Simplified continent outlines
    final continentPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.08)
      ..style = PaintingStyle.fill;

    // North America
    final northAmerica = Path()
      ..moveTo(size.width * 0.1, size.height * 0.3)
      ..lineTo(size.width * 0.35, size.height * 0.25)
      ..lineTo(size.width * 0.4, size.height * 0.6)
      ..lineTo(size.width * 0.15, size.height * 0.65)
      ..close();
    canvas.drawPath(northAmerica, continentPaint);

    // Europe
    final europe = Path()
      ..moveTo(size.width * 0.45, size.height * 0.25)
      ..lineTo(size.width * 0.6, size.height * 0.2)
      ..lineTo(size.width * 0.65, size.height * 0.45)
      ..lineTo(size.width * 0.5, size.height * 0.5)
      ..close();
    canvas.drawPath(europe, continentPaint);

    // Asia
    final asia = Path()
      ..moveTo(size.width * 0.6, size.height * 0.2)
      ..lineTo(size.width * 0.9, size.height * 0.15)
      ..lineTo(size.width * 0.95, size.height * 0.6)
      ..lineTo(size.width * 0.65, size.height * 0.65)
      ..close();
    canvas.drawPath(asia, continentPaint);

    // Africa
    final africa = Path()
      ..moveTo(size.width * 0.45, size.height * 0.5)
      ..lineTo(size.width * 0.6, size.height * 0.45)
      ..lineTo(size.width * 0.65, size.height * 0.85)
      ..lineTo(size.width * 0.5, size.height * 0.9)
      ..close();
    canvas.drawPath(africa, continentPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
